{"schemaVersion": 1, "id": "kairo", "version": "${version}", "name": "<PERSON><PERSON>", "description": "", "authors": [], "contact": {}, "license": "All-Rights-Reserved", "icon": "assets/kairo/icon.png", "environment": "*", "entrypoints": {"fabric-datagen": ["me.kairo.client.KairoDataGenerator"], "client": ["me.kairo.client.KairoClient"], "main": ["me.kairo.<PERSON>ro"]}, "mixins": ["kairo.mixins.json", {"config": "kairo.client.mixins.json", "environment": "client"}], "depends": {"fabricloader": ">=${loader_version}", "fabric": "*", "minecraft": "${minecraft_version}"}}
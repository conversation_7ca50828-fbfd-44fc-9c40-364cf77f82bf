<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="595fe8a2-65b3-44af-9759-d06915e35bd1" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <projects_view>
          <tree_state>
            <expand />
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 8
}]]></component>
  <component name="ProjectId" id="32gSDSRe41coTLk6BG3ZZSbwh02" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Gradle.C:/Users/<USER>/Downloads/Kairo [wrapper].executor": "Run",
    "Gradle.C:/Users/<USER>/Downloads/Kairo/build.gradle.executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true"
  }
}]]></component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="595fe8a2-65b3-44af-9759-d06915e35bd1" name="Changes" comment="" />
      <created>1757840604698</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1757840604698</updated>
    </task>
    <servers />
  </component>
</project>